"use client";

import { Suspense, useEffect, useState } from "react";
import { redirect, useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { z } from "zod";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from "@/components/ui/checkbox";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";

import { toast } from "sonner";
import {
  Loader2,
  Store,
  MapPin,
  Phone,
  Clock,
  CreditCard,
  Trash2,
  Plus,
  Building2,
  Globe,
  Hash,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  Dot,
  ShieldCheck,
  Zap,
  QrCode,
  ArrowLeft,
  Utensils,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from "@/components/ui/select";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { Progress } from "@/components/ui/progress";
import Image from "next/image";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import ErrorComponent from "@/components/ui/error";
import { FaWhatsapp } from "react-icons/fa6";
import RestaurantLoginSetup from "@/components/food/onboarding/restaurant-login-setup";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import WhatsAppSetupButton from "@/components/ui/whatsapp-setup-button";

const formSchema = z.object({
  restaurant: z
    .object({
      restaurant_name: z
        .string()
        .min(1, { message: "Restaurant name is required" }),
      restaurant_id: z.string().optional(),
      phone_number: z.string().refine(
        (val) => {
          // This placeholder will be replaced by conditional validation
          return true;
        },
        { message: "Valid phone number is required" }
      ),
      is_only_for_listing: z.boolean().default(false),
      inbox_access_enabled: z.boolean().optional().default(false),
      enable_support: z.boolean().optional().default(false),
      restaurant_auto_accept: z.boolean().optional().default(false),
      pickup_module: z.boolean().optional().default(false),
    })
    .refine(
      (data) => {
        // If is_only_for_listing is true, phone_number is optional
        if (data.is_only_for_listing) {
          return true;
        }
        // Otherwise, phone_number is required and must follow validation rules
        return (
          data.phone_number &&
          /^\d+$/.test(data.phone_number) &&
          data.phone_number.length >= 8 &&
          data.phone_number.length <= 15
        );
      },
      {
        message: "Phone number is required unless listing-only mode is enabled",
        path: ["phone_number"],
      }
    ),
  branches: z.array(
    z
      .object({
        branch_id: z.string().optional(),
        branch_name: z.string().min(1, { message: "Branch name is required" }).max(24, { message: "Branch name cannot exceed 24 characters" }).regex(/^[a-zA-Z\s]+$/, { message: "Branch name can only contain letters and spaces" }),
        branch_address: z
          .string()
          .min(1, { message: "Branch address is required" }),
        google_maps_link: z
          .string()
          .min(1, { message: "Google Maps link is required" }),
        latitude: z
          .number()
          .min(-90, { message: "Latitude must be between -90 and 90" })
          .max(90, { message: "Latitude must be between -90 and 90" })
          .refine((val) => val !== 0, {
            message: "Latitude must be set (cannot be 0)",
          }),
        longitude: z
          .number()
          .min(-180, { message: "Longitude must be between -180 and 180" })
          .max(180, { message: "Longitude must be between -180 and 180" })
          .refine((val) => val !== 0, {
            message: "Longitude must be set (cannot be 0)",
          }),
        branch_delivery: z.boolean().default(true),
        branch_pickup: z.boolean().default(false),
        payment_methods: z.object({
          cash: z.boolean().optional(),
          card: z.boolean().optional(),
          online: z.boolean().optional(),
        }).optional(),
        branch_preparation_time: z.number().min(0).optional(),
        branch_min_cart_amount: z.number().min(0).optional(),
        timings: z.array(
          z.object({
            day: z.number(),
            start: z.string(),
            end: z.string(),
            status: z.boolean(),
          })
        ),
        no_of_orders: z.number().min(0).optional(),
        driver_module: z.boolean().optional().default(false),
        payment_config: z.object({
          payment_method: z.enum(["ccavenue", "stripe", "tap", "network", "paytabs", "totalpay"]).optional(),
          access_code: z.string().optional(),
          merchant_id: z.number().optional(),
          working_key: z.string().optional(),
          payment_api_key: z.string().optional(),
          payment_webhook_secret: z.string().optional(),
          outlet_id: z.string().optional(),
          paytabs_profile_id: z.string().optional(),
          merchant_key: z.string().optional(),
          merchant_password: z.string().optional(),
        }).optional(),
      })
      .refine(
        (data) => {
          // At least one of branch_delivery or branch_pickup must be true
          return data.branch_delivery || data.branch_pickup;
        },
        {
          message: "At least one of delivery or pickup must be enabled",
          path: ["branch_pickup"], // Show error on pickup field
        }
      )
      .refine(
        (data) => {
          // If online payment is enabled, payment method must be selected
          if (data.payment_methods?.online) {
            return data.payment_config?.payment_method;
          }
          return true;
        },
        {
          message: "Payment gateway must be selected when online payment is enabled",
          path: ["payment_config", "payment_method"],
        }
      )
      .refine(
        (data) => {
          // If online payment is enabled and payment method is selected, validate required fields
          if (data.payment_methods?.online && data.payment_config?.payment_method) {
            const paymentMethod = data.payment_config.payment_method;
            const config = data.payment_config;

            switch (paymentMethod) {
              case "ccavenue":
                return config.access_code && config.merchant_id && config.working_key;
              case "stripe":
                return config.payment_api_key && config.payment_webhook_secret;
              case "tap":
                return config.payment_api_key;
              case "network":
                return config.outlet_id && config.payment_api_key;
              case "paytabs":
                return config.paytabs_profile_id && config.payment_api_key;
              case "totalpay":
                return config.merchant_key && config.merchant_password;
              default:
                return false;
            }
          }
          return true;
        },
        {
          message: "All required payment configuration fields must be filled",
          path: ["payment_config"],
        }
      )
  ),
});

type FormValues = z.infer<typeof formSchema>;

const defaultTimings = [
  { day: 0, start: "6:00 AM", end: "11:00 PM", status: true }, // Sunday
  { day: 1, start: "6:00 AM", end: "11:00 PM", status: true }, // Monday
  { day: 2, start: "6:00 AM", end: "11:00 PM", status: true }, // Tuesday
  { day: 3, start: "6:00 AM", end: "11:00 PM", status: true }, // Wednesday
  { day: 4, start: "6:00 AM", end: "11:00 PM", status: true }, // Thursday
  { day: 5, start: "6:00 AM", end: "11:00 PM", status: true }, // Friday
  { day: 6, start: "6:00 AM", end: "11:00 PM", status: true }, // Saturday
];

const defaultPaymentConfig = {
  payment_method: undefined,
  access_code: "",
  merchant_id: undefined,
  working_key: "",
  payment_api_key: "",
  payment_webhook_secret: "",
  outlet_id: "",
  paytabs_profile_id: "",
  merchant_key: "",
  merchant_password: "",
};

const dayNames = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

// Convert 24-hour format to AM/PM
const to12HourFormat = (time24: string) => {
  const [hours, minutes] = time24.split(":").map(Number);
  const period = hours >= 12 ? "PM" : "AM";
  const hours12 = hours % 12 || 12;
  return `${hours12}:${minutes.toString().padStart(2, "0")} ${period}`;
};

// Convert AM/PM format to 24-hour format
const to24HourFormat = (time12: string) => {
  const [time, period] = time12.split(" ");
  const [hours, minutes] = time.split(":").map(Number);
  let hours24 = hours;
  if (period === "PM" && hours !== 12) {
    hours24 = hours + 12;
  } else if (period === "AM" && hours === 12) {
    hours24 = 0;
  }
  return `${hours24.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
};

function OnboardingPage() {
  const [step, setStep] = useState(1);
  const totalSteps = 3;

  const [branchWeekendToggles, setBranchWeekendToggles] = useState<{
    [key: string]: boolean;
  }>({});
  const [branchData, setBranchData] = useState<any[]>([]);

  const [createNewError, setCreateNewError] = useState(false);

  const [selectedBranch, setSelectedBranch] = useState<string | undefined>(
    undefined
  );

  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const restaurantId = searchParams.get("id");

  const [onboardingComplete, setOnboardingComplete] = useState(false);
  const [currentRestaurantId, setCurrentRestaurantId] = useState<
    string | undefined
  >("");
  const [currentBranches, setCurrentBranches] = useState<
    Array<{ branch_id: string; branch_name: string }>
  >([]);
  const queryClient = useQueryClient();
  const handleBackToOnboarding = () => {
    setOnboardingComplete(false);
  };
  const { data: session } = useSession();


  const [isOnlyForListing, setIsOnlyForListing] = useState(false);

  useEffect(() => {
    if (!session) {
      // redirect("/login");
    }
  }, [session]);

  const defaultBranchPaymentMethods = { cash: false, card: false, online: false };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any,
    mode: "onChange",
    defaultValues: {
      restaurant: {
        restaurant_name: "",
        restaurant_id: restaurantId || "",
        phone_number: "",
        is_only_for_listing: false,
        inbox_access_enabled: false,
        enable_support: false,
        restaurant_auto_accept: false,
        pickup_module: false,
      },
      branches: [
        {
          branch_id: "",
          branch_name: "",
          branch_address: "",
          google_maps_link: "",
          latitude: 0,
          longitude: 0,
          branch_delivery: true,
          branch_pickup: false,
          payment_methods: defaultBranchPaymentMethods,
          branch_preparation_time: undefined,
          branch_min_cart_amount: undefined,
          timings: defaultTimings,
          no_of_orders: 0,
          driver_module: false,
          payment_config: defaultPaymentConfig,
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "branches",
    keyName: "fieldId",
  });

  // Store branch data when it changes
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.branches) {
        setBranchData(value.branches);
      }
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);

  // Restore branch data if fields are empty but we have stored data
  useEffect(() => {
    if (fields.length === 0 && branchData.length > 0) {
      branchData.forEach((branch) => {
        append(branch);
      });
    }
  }, [fields.length, branchData, append]);
  const addBranch = () => {
    const newBranch = {
      branch_id: "",
      branch_name: "",
      branch_address: "",
      google_maps_link: "",
      latitude: 0,
      longitude: 0,
      branch_delivery: true,
      branch_pickup: false,
      payment_methods: defaultBranchPaymentMethods,
      branch_preparation_time: undefined,
      branch_min_cart_amount: undefined,
      timings: defaultTimings,
      no_of_orders: 0,
      driver_module: false,
      payment_config: defaultPaymentConfig,
    };
    append(newBranch);
    setBranchWeekendToggles((prev) => ({
      ...prev,
      [newBranch.branch_id]: true,
    }));
  };

  const removeBranch = (index: number) => {
    const branch = fields[index];
    const branchId = branch.branch_id || "";
    if (branch.no_of_orders === 0) {
      remove(index);
      setBranchWeekendToggles((prev) => {
        const updated = { ...prev };
        delete updated[branchId];
        return updated;
      });

      toast.success("Branch removed successfully");
    } else {
      toast.error("Cannot remove branch with orders");
    }
  };

  const createRestaurantMutation = useMutation({
    mutationFn: async (data: {
      restaurant: {
        restaurant_name: string;
        phone_number: string;
        is_only_for_listing: boolean;
        inbox_access_enabled?: boolean;
        enable_support?: boolean;
        restaurant_auto_accept?: boolean;
        pickup_module?: boolean;
      };
    }) => {
      setIsSubmitting(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/onboard-new-restaurant`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            restaurant: {
              ...data.restaurant,
              restaurant_id: data.restaurant.restaurant_name
                .trim()
                .replace(/\s+/g, "-"),
              is_only_for_listing: data.restaurant.is_only_for_listing,
              inbox_access_enabled: data.restaurant.inbox_access_enabled || false,
              enable_support: data.restaurant.enable_support || false,
              restaurant_auto_accept: data.restaurant.restaurant_auto_accept || false,
              pickup_module: data.restaurant.pickup_module || false,
            },
          }),
        }
      );

      const responseData = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 409) {
          throw new (globalThis.Error)(responseData.message || "Restaurant with this name already exists. Please choose a different name.");
        } else if (response.status === 400) {
          throw new (globalThis.Error)(responseData.message || "Invalid restaurant details. Please check your information.");
        } else {
          throw new (globalThis.Error)(responseData.message || "Failed to create restaurant. Please try again.");
        }
      }

      return responseData;
    },
    onSuccess: (data) => {
      if (data?.statusCode === 201) {
        // Update URL with restaurant ID
        const params = new URLSearchParams(searchParams.toString() as string);
        params.set("id", data.message);
        router.push(`/admin/food/onboarding?${params.toString()}`);
        setIsSubmitting(false);
        toast.success("Successfully Created Restaurant!");
        setCreateNewError(false);
      } else {
        setCreateNewError(true);
        setIsSubmitting(false);
        toast.error(data?.message || "Failed to create restaurant. Please try again.");
      }
    },
    onError: (error: any) => {
      setCreateNewError(true);
      setIsSubmitting(false);
      const errorMessage = error?.message || "Failed to create restaurant. Please try again.";
      toast.error(errorMessage);
      
      // Stay on step 1 and focus on restaurant name field if it's a conflict error
      if (errorMessage.includes("already exists") || errorMessage.includes("Restaurant with")) {
        // Set focus back to restaurant name field
        setTimeout(() => {
          const restaurantNameInput = document.querySelector('input[name="restaurant.restaurant_name"]') as HTMLInputElement;
          if (restaurantNameInput) {
            restaurantNameInput.focus();
            restaurantNameInput.select();
          }
        }, 100);
      }
    },
  });

  const { mutateAsync: submitOnboarding } = useMutation({
    mutationKey: ["restaurantOnboarding"],
    mutationFn: async (data: FormValues) => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/onboard-new-restaurant/update-details`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            restaurant: {
              restaurant_id: data.restaurant.restaurant_id,
              restaurant_name: data.restaurant.restaurant_name.trim(),
              phone_number: data.restaurant.phone_number,
              is_only_for_listing: data.restaurant.is_only_for_listing,
              inbox_access_enabled: data.restaurant.inbox_access_enabled || false,
              enable_support: data.restaurant.enable_support || false,
              restaurant_auto_accept: data.restaurant.restaurant_auto_accept || false,
              pickup_module: data.restaurant.pickup_module || false,
            },
            branches: data.branches.map((branch) => {
              // Handle payment config based on payment method
              let paymentConfig: {
                payment_method?: "ccavenue" | "stripe" | "tap" | "network" | "paytabs" | "totalpay";
                access_code?: string;
                merchant_id?: number;
                working_key?: string;
                payment_api_key?: string;
                payment_webhook_secret?: string;
                outlet_id?: string;
                paytabs_profile_id?: string;
                merchant_key?: string;
                merchant_password?: string;
              } | undefined = undefined;

              if (branch.payment_methods?.online && branch.payment_config?.payment_method) {
                switch (branch.payment_config.payment_method) {
                  case "ccavenue":
                    paymentConfig = {
                      payment_method: "ccavenue",
                      access_code: branch.payment_config.access_code,
                      merchant_id: branch.payment_config.merchant_id,
                      working_key: branch.payment_config.working_key,
                    };
                    break;
                  case "stripe":
                    paymentConfig = {
                      payment_method: "stripe",
                      payment_api_key: branch.payment_config.payment_api_key,
                      payment_webhook_secret: branch.payment_config.payment_webhook_secret,
                    };
                    break;
                  case "tap":
                    paymentConfig = {
                      payment_method: "tap",
                      payment_api_key: branch.payment_config.payment_api_key,
                    };
                    break;
                  case "network":
                    paymentConfig = {
                      payment_method: "network",
                      outlet_id: branch.payment_config.outlet_id,
                      payment_api_key: branch.payment_config.payment_api_key,
                    };
                    break;
                  case "paytabs":
                    paymentConfig = {
                      payment_method: "paytabs",
                      paytabs_profile_id: branch.payment_config.paytabs_profile_id,
                      payment_api_key: branch.payment_config.payment_api_key,
                    };
                    break;
                  case "totalpay":
                    paymentConfig = {
                      payment_method: "totalpay",
                      merchant_key: branch.payment_config.merchant_key,
                      merchant_password: branch.payment_config.merchant_password,
                    };
                    break;
                }
              }

              return {
                branch_id:
                  branch.branch_id ||
                  `${data.restaurant.restaurant_id}-${branch.branch_name
                    .trim()
                    .replace(/\s+/g, "-")}`,
                branch_name: branch.branch_name.trim(),
                branch_address: branch.branch_address,
                google_maps_link: branch.google_maps_link,
                latitude: branch.latitude,
                longitude: branch.longitude,
                branch_delivery: branch.branch_delivery,
                branch_pickup: branch.branch_pickup,
                payment_methods: branch.payment_methods || defaultBranchPaymentMethods,
                branch_preparation_time: branch.branch_preparation_time,
                branch_min_cart_amount: branch.branch_min_cart_amount,
                timings: branch.timings,
                driver_module: branch.driver_module || false,
                payment_config: paymentConfig,
              };
            }),
          }),
        }
      );
      return response.json();
    },
    onSuccess: (status) => {
      if (status.statusCode !== 201) {
        toast.error("Failed to submit restaurant details. Please try again.");
      } else {
        toast.success("Successfully Updated Restaurant Details!");
        refetchRestaurant();
      }
    },
    onError: (error) => {
      toast.error("Failed to update restaurant details. Please try again.");
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);

    try {
      if (step === 3) {
        // Validate required fields before submission
        const isFormValid = await form.trigger();
        if (!isFormValid) {
          toast.error("Please fix all form errors before submitting");
          setIsSubmitting(false);
          return;
        }

        // Also validate custom step validation (including payment config)
        const isStepValid = await validateStep();
        if (!isStepValid) {
          setIsSubmitting(false);
          return;
        }

        // Transform the data to match the expected format
        const transformedData = {
          restaurant: {
            restaurant_id: values.restaurant.restaurant_id,
            restaurant_name: values.restaurant.restaurant_name.trim(),
            phone_number: values.restaurant.phone_number,
            is_only_for_listing: values.restaurant.is_only_for_listing,
            inbox_access_enabled: values.restaurant.inbox_access_enabled,
            enable_support: values.restaurant.enable_support,
            restaurant_auto_accept: values.restaurant.restaurant_auto_accept,
            pickup_module: values.restaurant.pickup_module,
          },
          branches: values.branches.map((branch) => {
            // Handle payment config based on payment method
            let paymentConfig: {
              payment_method?: "ccavenue" | "stripe" | "tap" | "network" | "paytabs" | "totalpay";
              access_code?: string;
              merchant_id?: number;
              working_key?: string;
              payment_api_key?: string;
              payment_webhook_secret?: string;
              outlet_id?: string;
              paytabs_profile_id?: string;
              merchant_key?: string;
              merchant_password?: string;
            } | undefined = undefined;

            if (branch.payment_methods?.online && branch.payment_config?.payment_method) {
              switch (branch.payment_config.payment_method) {
                case "ccavenue":
                  paymentConfig = {
                    payment_method: "ccavenue",
                    access_code: branch.payment_config.access_code,
                    merchant_id: branch.payment_config.merchant_id,
                    working_key: branch.payment_config.working_key,
                  };
                  break;
                case "stripe":
                  paymentConfig = {
                    payment_method: "stripe",
                    payment_api_key: branch.payment_config.payment_api_key,
                    payment_webhook_secret: branch.payment_config.payment_webhook_secret,
                  };
                  break;
                case "tap":
                  paymentConfig = {
                    payment_method: "tap",
                    payment_api_key: branch.payment_config.payment_api_key,
                  };
                  break;
                case "network":
                  paymentConfig = {
                    payment_method: "network",
                    outlet_id: branch.payment_config.outlet_id,
                    payment_api_key: branch.payment_config.payment_api_key,
                  };
                  break;
                case "paytabs":
                  paymentConfig = {
                    payment_method: "paytabs",
                    paytabs_profile_id: branch.payment_config.paytabs_profile_id,
                    payment_api_key: branch.payment_config.payment_api_key,
                  };
                  break;
                case "totalpay":
                  paymentConfig = {
                    payment_method: "totalpay",
                    merchant_key: branch.payment_config.merchant_key,
                    merchant_password: branch.payment_config.merchant_password,
                  };
                  break;
              }
            }

            return {
              branch_id: branch.branch_id || `${values.restaurant.restaurant_id}-${branch.branch_name.trim().replace(/\s+/g, "-")}`,
              branch_name: branch.branch_name.trim(),
              branch_address: branch.branch_address,
              google_maps_link: branch.google_maps_link,
              latitude: branch.latitude,
              longitude: branch.longitude,
              branch_delivery: branch.branch_delivery,
              branch_pickup: branch.branch_pickup,
              payment_methods: branch.payment_methods || defaultBranchPaymentMethods,
              branch_preparation_time: branch.branch_preparation_time,
              branch_min_cart_amount: branch.branch_min_cart_amount,
              timings: branch.timings,
              driver_module: branch.driver_module || false,
              payment_config: paymentConfig,
            };
          }),
        };

        try {
          const result = await submitOnboarding(transformedData);
          
          if (result) {
            queryClient.invalidateQueries({ queryKey: ["getRestaurants"] });
            await refetchRestaurant();
            toast.success("Restaurant details updated successfully!");
          }
        } catch (submitError: any) {
          const errorMessage = submitError?.message || 'Unknown error';
          toast.error(`Failed to update restaurant details: ${errorMessage}`);
        }
      } else {
        toast.error("Please complete all steps before submitting");
      }
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error occurred';
      toast.error(`Failed to update restaurant details: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateStep = async () => {
    let fieldsToValidate: string[] = [];

    switch (step) {
      case 1:
        fieldsToValidate = [
          "restaurant.restaurant_name",
          "restaurant.phone_number",
          "restaurant.inbox_access_enabled",
          "restaurant.enable_support",
          "restaurant.restaurant_auto_accept",
          "restaurant.pickup_module",
        ];
        break;
      case 2:
        fieldsToValidate = fields.flatMap((_, index) => [
          `branches.${index}.branch_name`,
          `branches.${index}.branch_address`,
          `branches.${index}.google_maps_link`,
          `branches.${index}.latitude`,
          `branches.${index}.longitude`,
          `branches.${index}.branch_delivery`,
          `branches.${index}.branch_pickup`,
          `branches.${index}.payment_methods`,
          `branches.${index}.branch_preparation_time`,
          `branches.${index}.branch_min_cart_amount`,
          `branches.${index}.driver_module`,
        ]);
        break;
      case 3:
        fieldsToValidate = fields.flatMap((_, index) => [
          `branches.${index}.timings`,
          // Add payment config validation if online payment is enabled
          ...(form.getValues(`branches.${index}.payment_methods.online`) ? [
            `branches.${index}.payment_config.payment_method`,
            `branches.${index}.payment_config.payment_api_key`,
          ] : []),
        ]);
        break;
    }

    const result = await form.trigger(fieldsToValidate as any);

    // Show specific validation errors
    if (!result) {
      const errors = form.formState.errors;
      let errorMessages: string[] = [];

      // Check restaurant level errors for Step 1
      if (step === 1 && errors.restaurant) {
        if (errors.restaurant.restaurant_name) {
          errorMessages.push(`• Restaurant Name: ${errors.restaurant.restaurant_name.message}`);
        }
        if (errors.restaurant.phone_number) {
          errorMessages.push(`• WhatsApp Number: ${errors.restaurant.phone_number.message}`);
        }
      }

      // Check branch level errors for Step 2 and 3
      if ((step === 2 || step === 3) && errors.branches && Array.isArray(errors.branches)) {
        errors.branches.forEach?.((branchError: any, index: number) => {
          if (branchError && typeof branchError === 'object') {
            const branchName = form.getValues(`branches.${index}.branch_name`) || `Branch ${index + 1}`;

            // Step 2 specific validations
            if (step === 2) {
              if (branchError.branch_name?.message) {
                errorMessages.push(`• ${branchName} - Name: ${branchError.branch_name.message}`);
              }
              if (branchError.branch_address?.message) {
                errorMessages.push(`• ${branchName} - Address: ${branchError.branch_address.message}`);
              }
              if (branchError.google_maps_link?.message) {
                errorMessages.push(`• ${branchName} - Google Maps Link: ${branchError.google_maps_link.message}`);
              }
              if (branchError.latitude?.message) {
                errorMessages.push(`• ${branchName} - Latitude: ${branchError.latitude.message}`);
              }
              if (branchError.longitude?.message) {
                errorMessages.push(`• ${branchName} - Longitude: ${branchError.longitude.message}`);
              }
            }

            // Step 3 specific validations (payment config)
            if (step === 3 && branchError.payment_config) {
              if (branchError.payment_config.payment_method?.message) {
                errorMessages.push(`• ${branchName} - Payment Gateway: ${branchError.payment_config.payment_method.message}`);
              }
              if (branchError.payment_config.payment_api_key?.message) {
                errorMessages.push(`• ${branchName} - API Key: ${branchError.payment_config.payment_api_key.message}`);
              }
              if (branchError.payment_config.access_code?.message) {
                errorMessages.push(`• ${branchName} - Access Code: ${branchError.payment_config.access_code.message}`);
              }
              if (branchError.payment_config.merchant_id?.message) {
                errorMessages.push(`• ${branchName} - Merchant ID: ${branchError.payment_config.merchant_id.message}`);
              }
              if (branchError.payment_config.working_key?.message) {
                errorMessages.push(`• ${branchName} - Working Key: ${branchError.payment_config.working_key.message}`);
              }
            }
          }
        });
      }

      if (errorMessages.length > 0) {
        const stepName = step === 1 ? "Restaurant Information" : step === 2 ? "Branch Details" : "Payment & Timing Configuration";
        toast.error(`${stepName} - Please fix the following:\n\n${errorMessages.join('\n')}`, {
          duration: 6000,
        });
      } else {
        const stepName = step === 1 ? "Restaurant Information" : step === 2 ? "Branch Details" : "Payment & Timing Configuration";
        toast.error(`${stepName} - Please fill in all required fields before proceeding.`);
      }
      return false;
    }

    // Special check for latitude/longitude being non-zero when in step 2 or 3
    if ((step === 2 || step === 3) && result) {
      const branches = form.getValues("branches");
      let locationErrors: string[] = [];

      for (let i = 0; i < branches.length; i++) {
        const branchName = branches[i].branch_name || `Branch ${i + 1}`;
        if (branches[i].latitude === 0 || branches[i].longitude === 0) {
          locationErrors.push(`• ${branchName}: Please set valid latitude and longitude coordinates`);
        }
      }

      if (locationErrors.length > 0) {
        toast.error(`Branch Location - Please fix the following:\n\n${locationErrors.join('\n')}`, {
          duration: 5000,
        });
        return false;
      }
    }

    // Special check for delivery/pickup when in step 2
    if (step === 2 && result) {
      const branches = form.getValues("branches");
      let serviceErrors: string[] = [];

      for (let i = 0; i < branches.length; i++) {
        const branchName = branches[i].branch_name || `Branch ${i + 1}`;
        if (!branches[i].branch_delivery && !branches[i].branch_pickup) {
          serviceErrors.push(`• ${branchName}: Enable at least delivery or pickup service`);
        }
      }

      if (serviceErrors.length > 0) {
        toast.error(`Branch Services - Please fix the following:\n\n${serviceErrors.join('\n')}`, {
          duration: 5000,
        });
        return false;
      }
    }

    // Special check for payment configuration when online payment is enabled
    if (step === 3 && result) {
      const branches = form.getValues("branches");
      let paymentErrors: string[] = [];

      for (let i = 0; i < branches.length; i++) {
        const branchName = branches[i].branch_name || `Branch ${i + 1}`;
        if (branches[i].payment_methods?.online) {
          const paymentConfig = branches[i].payment_config;
          if (!paymentConfig?.payment_method) {
            paymentErrors.push(`• ${branchName}: Please select a payment gateway`);
            continue;
          }

          // Add additional validation based on payment method
          switch (paymentConfig.payment_method) {
            case "ccavenue":
              const missingCCAvenue: string[] = [];
              if (!paymentConfig.access_code) missingCCAvenue.push("Access Code");
              if (!paymentConfig.merchant_id) missingCCAvenue.push("Merchant ID");
              if (!paymentConfig.working_key) missingCCAvenue.push("Working Key");
              if (missingCCAvenue.length > 0) {
                paymentErrors.push(`• ${branchName}: CCAvenue - Missing: ${missingCCAvenue.join(", ")}`);
              }
              break;

            case "stripe":
              const missingStripe: string[] = [];
              if (!paymentConfig.payment_api_key) missingStripe.push("API Key");
              if (!paymentConfig.payment_webhook_secret) missingStripe.push("Webhook Secret");
              if (missingStripe.length > 0) {
                paymentErrors.push(`• ${branchName}: Stripe - Missing: ${missingStripe.join(", ")}`);
              }
              break;

            case "tap":
              if (!paymentConfig.payment_api_key) {
                paymentErrors.push(`• ${branchName}: Tap - Missing: API Key`);
              }
              break;

            case "network":
              const missingNetwork: string[] = [];
              if (!paymentConfig.outlet_id) missingNetwork.push("Outlet ID");
              if (!paymentConfig.payment_api_key) missingNetwork.push("API Key");
              if (missingNetwork.length > 0) {
                paymentErrors.push(`• ${branchName}: Network - Missing: ${missingNetwork.join(", ")}`);
              }
              break;

            case "paytabs":
              const missingPayTabs: string[] = [];
              if (!paymentConfig.paytabs_profile_id) missingPayTabs.push("Profile ID");
              if (!paymentConfig.payment_api_key) missingPayTabs.push("API Key");
              if (missingPayTabs.length > 0) {
                paymentErrors.push(`• ${branchName}: PayTabs - Missing: ${missingPayTabs.join(", ")}`);
              }
              break;

            case "totalpay":
              const missingTotalPay: string[] = [];
              if (!paymentConfig.merchant_key) missingTotalPay.push("Merchant Key");
              if (!paymentConfig.merchant_password) missingTotalPay.push("Merchant Password");
              if (missingTotalPay.length > 0) {
                paymentErrors.push(`• ${branchName}: TotalPay - Missing: ${missingTotalPay.join(", ")}`);
              }
              break;
          }
        }
      }

      if (paymentErrors.length > 0) {
        toast.error(`Payment Configuration - Please fix the following:\n\n${paymentErrors.join('\n')}`, {
          duration: 8000,
        });
        return false;
      }
    }

    return result;
  };

  const nextStep = async () => {
    const isValid = await validateStep();
    if (!isValid) {
      // validateStep() already shows specific error messages, no need for generic message
      return;
    }
    
    setCreateNewError(false);
    // Store the current form values before moving to next step
    const currentValues = form.getValues();

    try {
      if (step === 1 && !restaurantId) {
        // Try to create the restaurant and wait for success before proceeding
        await createRestaurantMutation.mutateAsync({
          restaurant: {
            restaurant_name: currentValues.restaurant.restaurant_name,
            phone_number: currentValues.restaurant.phone_number,
            is_only_for_listing: currentValues.restaurant.is_only_for_listing,
            inbox_access_enabled: currentValues.restaurant.inbox_access_enabled,
            enable_support: currentValues.restaurant.enable_support,
            restaurant_auto_accept: currentValues.restaurant.restaurant_auto_accept,
            pickup_module: currentValues.restaurant.pickup_module,
          },
        });
        // If we reach here, restaurant creation was successful
        // The onSuccess handler will handle URL update and step progression
        return;
      }

      // For other steps or when restaurantId already exists
      if (step < totalSteps) {
        setStep(step + 1);
        form.reset(currentValues);
      }
    } catch (error: any) {
      console.error("Error during step transition:", error);
      // Don't show additional toast as the mutation onError already handles it
      // Just ensure we stay on the current step
      setCreateNewError(true);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      // Store the current form values before moving to previous step
      const currentValues = form.getValues();
      setStep(step - 1);
      // Ensure form values are preserved after step change
      form.reset(currentValues);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Restaurant Information
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                {"Let's start with your restaurant's basic details"}
              </p>
            </div>

            <div className="space-y-6">
              <FormField
                control={form.control}
                name="restaurant.restaurant_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Restaurant Name</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          className={cn(
                            "h-11 pl-10",
                            createNewError && "border-red-500 focus:border-red-500 ring-red-500"
                          )}
                          placeholder="e.g., The Gourmet Kitchen"
                          onChange={(e) => {
                            field.onChange(e);
                            // Reset error state when user starts typing
                            if (createNewError) {
                              setCreateNewError(false);
                            }
                          }}
                        />
                        <Store className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                      </div>
                    </FormControl>
                    <FormMessage />
                    {createNewError && (
                      <p className="text-xs text-red-500 mt-1">
                        Restaurant name already exists. Please choose a different name.
                      </p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="restaurant.phone_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Whatsapp Number</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          className={cn(
                            "h-11 pl-10",
                            form.formState.errors.restaurant?.phone_number &&
                            "border-red-500 focus:border-red-500 ring-red-500"
                          )}
                          type="text"
                          maxLength={15}
                          placeholder="971XXXXXXXXX"
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, "");
                            field.onChange(value);
                            // Trigger validation immediately
                            form.trigger("restaurant.phone_number");
                          }}
                        />
                        <Phone className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                      </div>
                    </FormControl>
                    <FormDescription className="text-xs text-gray-500">
                      Enter a valid phone number (8-15 digits) - Ex: 971XXXXXXXX
                    </FormDescription>
                    <FormMessage className="text-xs mt-1 text-red-500 font-medium" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="restaurant.is_only_for_listing"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Listing Only Mode
                      </FormLabel>
                      <FormDescription>
                        Enable if this restaurant is only for listing purposes
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          // setIsOnlyForListing(checked);
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {restaurantId && <div className="space-y-4">
                <h4 className="text-base font-medium text-gray-900">
                  Restaurant Features
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="restaurant.inbox_access_enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Inbox Access
                          </FormLabel>
                          <FormDescription>
                            Give inbox module access to merchant
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="restaurant.enable_support"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Support Module
                          </FormLabel>
                          <FormDescription>
                            Enable support functionality on WhatsApp
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="restaurant.restaurant_auto_accept"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Auto Accept Orders
                          </FormLabel>
                          <FormDescription>
                            Automatically accept incoming orders
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="restaurant.pickup_module"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Pickup Module
                          </FormLabel>
                          <FormDescription>
                            Enable pickup module
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">
                  Branch Details ({form.getValues("restaurant.restaurant_name")}
                  )
                </h2>
                <p className="mt-1 text-sm text-gray-600">
                  Add your restaurant branches
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={addBranch}
                className="bg-white hover:bg-gray-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Branch
              </Button>
            </div>

            <div className="space-y-6">
              {fields.map((field, index) => (
                <div
                  key={field.fieldId}
                  className="space-y-6 p-6 bg-white rounded-lg border"
                >
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Branch {index + 1}</h3>
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={() => removeBranch(index)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remove Branch
                      </Button>
                    )}
                  </div>

                  <div className="grid gap-6">
                    <FormField
                      control={form.control}
                      name={`branches.${index}.branch_name`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Branch Name</FormLabel>
                          <FormDescription>
                            e.g., Abhudhabi (only letters and spaces, max 24 characters)
                          </FormDescription>
                          <FormControl>
                            <Input
                              {...field}
                              className="h-11"
                              placeholder="e.g., Abhudhabi"
                              maxLength={24}
                              onChange={(e) => {
                                // Only allow letters and spaces, max 24 characters
                                const value = e.target.value.replace(/[^a-zA-Z\s]/g, '').slice(0, 24);
                                field.onChange(value);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`branches.${index}.branch_address`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Branch Address</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              className="h-11"
                              placeholder="e.g., Abhudhabi"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`branches.${index}.google_maps_link`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Google Maps Link</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                {...field}
                                className="h-11 pl-10"
                                placeholder="https://maps.google.com/..."
                              />
                              <Globe className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`branches.${index}.latitude`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Latitude</FormLabel>
                            <FormDescription className="text-xs text-gray-500">
                              Must be a value between -90 and 90 (cannot be 0)
                            </FormDescription>
                            <FormControl>
                              <Input
                                type="number"
                                step="any"
                                placeholder="Enter latitude (e.g. 25.276987)"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseFloat(e.target.value))
                                }
                                className={cn(
                                  field.value === 0 &&
                                  "border-red-300 focus-visible:ring-red-400"
                                )}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />{" "}
                      <FormField
                        control={form.control}
                        name={`branches.${index}.longitude`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Longitude</FormLabel>
                            <FormDescription className="text-xs text-gray-500">
                              Must be a value between -180 and 180 (cannot be 0)
                            </FormDescription>
                            <FormControl>
                              <Input
                                type="number"
                                step="any"
                                placeholder="Enter longitude (e.g. 55.296249)"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseFloat(e.target.value))
                                }
                                className={cn(
                                  field.value === 0 &&
                                  "border-red-300 focus-visible:ring-red-400"
                                )}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Delivery and Pickup Options */}
                    <div className="space-y-4">
                      <h4 className="text-base font-medium text-gray-900">
                        Service Options
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`branches.${index}.branch_delivery`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-secondary/10 transition-colors">
                              <div className="space-y-0.5">
                                <FormLabel>Delivery Service</FormLabel>
                                <FormDescription className="text-xs text-muted-foreground">
                                  Enable delivery orders for this branch
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`branches.${index}.branch_pickup`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-secondary/10 transition-colors">
                              <div className="space-y-0.5">
                                <FormLabel>Pickup Service</FormLabel>
                                <FormDescription className="text-xs text-muted-foreground">
                                  Enable pickup orders for this branch
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Show validation error if neither option is selected */}
                      {!form.getValues(`branches.${index}.branch_delivery`) &&
                        !form.getValues(`branches.${index}.branch_pickup`) && (
                          <p className="text-xs text-destructive mt-1">
                            At least one of delivery or pickup must be enabled
                          </p>
                        )}
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name={`branches.${index}.driver_module`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-secondary/10 transition-colors">
                            <div className="space-y-0.5">
                              <FormLabel>Driver Module</FormLabel>
                              <FormDescription className="text-xs text-muted-foreground">
                                Enable driver module
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value || false}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-base font-medium text-gray-900">Payment & Order Settings</h4>
                      <div className="grid grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name={`branches.${index}.payment_methods.cash`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg bg-white p-4 shadow-sm border">
                              <div className="space-y-0.5">
                                <FormLabel>Cash</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={!!field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`branches.${index}.payment_methods.card`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg bg-white p-4 shadow-sm border">
                              <div className="space-y-0.5">
                                <FormLabel>Card</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={!!field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`branches.${index}.payment_methods.online`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg bg-white p-4 shadow-sm border">
                              <div className="space-y-0.5">
                                <FormLabel>Online</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={!!field.value}
                                  onCheckedChange={(checked) => {
                                    field.onChange(checked);
                                    // Reset payment config when online payment is disabled
                                    if (!checked) {
                                      form.setValue(`branches.${index}.payment_config`, defaultPaymentConfig);
                                    }
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Show payment configuration only if online payment is enabled */}
                      {form.watch(`branches.${index}.payment_methods`)?.online && (
                        <div className="space-y-4 mt-6 p-4 border rounded-lg bg-gray-50">
                          <FormField
                            control={form.control}
                            name={`branches.${index}.payment_config.payment_method`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Payment Gateway Configuration</FormLabel>
                                <Select
                                  value={field.value}
                                  onValueChange={(value: "ccavenue" | "stripe" | "tap" | "network" | "paytabs" | "totalpay") => {
                                    field.onChange(value);
                                    // Reset other fields when payment method changes
                                    form.setValue(`branches.${index}.payment_config`, {
                                      ...defaultPaymentConfig,
                                      payment_method: value,
                                    });
                                  }}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select payment gateway" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="ccavenue">CCAvenue</SelectItem>
                                    <SelectItem value="stripe">Stripe</SelectItem>
                                    <SelectItem value="tap">Tap</SelectItem>
                                    {/* <SelectItem value="network">Network</SelectItem>
                                    <SelectItem value="paytabs">PayTabs</SelectItem> */}
                                    <SelectItem value="totalpay">TotalPay</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {form.watch(`branches.${index}.payment_config.payment_method`) === "ccavenue" && (
                            <>
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.access_code`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Access Code</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter CCAvenue access code" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.merchant_id`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Merchant ID</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        {...field}
                                        onChange={(e) => field.onChange(Number(e.target.value))}
                                        placeholder="Enter merchant ID"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.working_key`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Working Key</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter working key" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </>
                          )}

                          {form.watch(`branches.${index}.payment_config.payment_method`) === "stripe" && (
                            <>
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.payment_api_key`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Stripe API Key</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter Stripe API key" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.payment_webhook_secret`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Stripe Webhook Secret</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter Stripe webhook secret" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </>
                          )}

                          {form.watch(`branches.${index}.payment_config.payment_method`) === "tap" && (
                            <FormField
                              control={form.control}
                              name={`branches.${index}.payment_config.payment_api_key`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Tap API Key</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder="Enter Tap API key" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          {form.watch(`branches.${index}.payment_config.payment_method`) === "network" && (
                            <>
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.outlet_id`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Outlet ID</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter outlet ID" />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.payment_api_key`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>API Key</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter API key" />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                            </>
                          )}

                          {form.watch(`branches.${index}.payment_config.payment_method`) === "paytabs" && (
                            <>
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.paytabs_profile_id`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>PayTabs Profile ID</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter PayTabs profile ID" />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.payment_api_key`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>API Key</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter API key" />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                            </>
                          )}

                          {form.watch(`branches.${index}.payment_config.payment_method`) === "totalpay" && (
                            <>
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.merchant_key`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Merchant Key</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter merchant key" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`branches.${index}.payment_config.merchant_password`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Merchant Password</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter merchant password" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </>
                          )}
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <FormField
                          control={form.control}
                          name={`branches.${index}.branch_preparation_time`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Preparation Time (minutes)</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    {...field}
                                    type="number"
                                    value={field.value ?? ''}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                    className="h-11 pl-10"
                                    placeholder="e.g., 25"
                                  />
                                  <Clock className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`branches.${index}.branch_min_cart_amount`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Minimum Order Amount</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    {...field}
                                    type="number"
                                    value={field.value ?? ''}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                    className="pl-12 h-11"
                                    placeholder="0.00"
                                    step="0.01"
                                  />
                                  <span className="absolute left-3 top-2.5 text-gray-400">
                                    AED
                                  </span>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Branch Timings ({form.getValues("restaurant.restaurant_name")})
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                {"Set your restaurant branches' operating hours"}
              </p>
            </div>

            <div className="space-y-6">
              {fields.map((field, branchIndex) => (
                <Accordion
                  key={field.fieldId}
                  type="single"
                  value={selectedBranch}
                  onValueChange={setSelectedBranch}
                  collapsible
                  className="bg-white rounded-lg border"
                >
                  <AccordionItem
                    value={`branch-${branchIndex}`}
                    className="border-none"
                  >
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                      <div className="flex flex-col items-start">
                        <h3 className="text-lg font-medium text-gray-900">
                          {"Branch-" + (branchIndex + 1)} :{" "}
                          {form.getValues(
                            `branches.${branchIndex}.branch_name`
                          ) || `Branch ${branchIndex + 1}`}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          Configure operating hours
                        </p>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                      <div className="space-y-4">
                        <div className="space-y-1">
                          <div className="grid grid-cols-[1fr,2fr] gap-4 py-2 px-4 bg-gray-50 rounded-t-lg">
                            <div className="text-sm font-medium text-gray-500">
                              Day
                            </div>
                            <div className="text-sm font-medium text-gray-500">
                              Hours
                            </div>
                          </div>
                          {dayNames.map((dayName, dayIndex) => (
                            <div
                              key={dayIndex}
                              className={`grid grid-cols-[1fr,2fr] gap-4 py-3 px-4 items-center ${dayIndex === dayNames.length - 1
                                ? "rounded-b-lg"
                                : "border-b"
                                } hover:bg-gray-50 transition-colors`}
                            >
                              <div className="flex items-center space-x-2">
                                <FormField
                                  control={form.control}
                                  name={`branches.${branchIndex}.timings.${dayIndex}.status`}
                                  render={({ field }) => (
                                    <FormItem className="flex items-center space-x-2">
                                      <Checkbox
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                        className="rounded-sm"
                                      />
                                      <Label
                                        className={`font-medium ${field.value
                                          ? "text-gray-900"
                                          : "text-gray-400"
                                          } `}
                                        style={{
                                          marginTop: "1px",
                                        }}
                                      >
                                        {dayName}
                                      </Label>
                                    </FormItem>
                                  )}
                                />
                              </div>
                              <div className="flex items-center space-x-4">
                                <FormField
                                  control={form.control}
                                  name={`branches.${branchIndex}.timings.${dayIndex}.start`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input
                                          type="time"
                                          value={
                                            field.value
                                              ? to24HourFormat(field.value)
                                              : ""
                                          }
                                          onChange={(e) => {
                                            if (e.target.value) {
                                              field.onChange(
                                                to12HourFormat(e.target.value)
                                              );
                                            }
                                          }}
                                          disabled={
                                            !form.getValues(
                                              `branches.${branchIndex}.timings.${dayIndex}.status`
                                            )
                                          }
                                          className="w-[120px] h-9 bg-white"
                                        />
                                      </FormControl>
                                    </FormItem>
                                  )}
                                />
                                <span className="text-gray-400">to</span>
                                <FormField
                                  control={form.control}
                                  name={`branches.${branchIndex}.timings.${dayIndex}.end`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input
                                          type="time"
                                          value={
                                            field.value
                                              ? to24HourFormat(field.value)
                                              : ""
                                          }
                                          onChange={(e) => {
                                            if (e.target.value) {
                                              field.onChange(
                                                to12HourFormat(e.target.value)
                                              );
                                            }
                                          }}
                                          disabled={
                                            !form.getValues(
                                              `branches.${branchIndex}.timings.${dayIndex}.status`
                                            )
                                          }
                                          className="w-[120px] h-9 bg-white"
                                        />
                                      </FormControl>
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="flex items-center justify-end space-x-4 mt-4 pt-4 border-t">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const firstDay = form.getValues(
                                `branches.${branchIndex}.timings.0`
                              );
                              dayNames.forEach((_, idx) => {
                                if (idx > 0) {
                                  const currentDay = form.getValues(
                                    `branches.${branchIndex}.timings.${idx}`
                                  );
                                  form.setValue(
                                    `branches.${branchIndex}.timings.${idx}`,
                                    {
                                      day: currentDay.day, // Preserve the day index (0,1,2,3,etc.)
                                      start: firstDay.start, // Copy Sunday's start time
                                      end: firstDay.end, // Copy Sunday's end time
                                      status: currentDay.status, // Preserve the status (open/closed)
                                    }
                                  );
                                }
                              });
                              toast.success("Sunday hours applied to all days");
                            }}
                            className="text-sm"
                          >
                            {"Apply Sunday's Hours to All Days"}
                          </Button>
                          {branchIndex === 0 && fields.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const sourceBranch =
                                  form.getValues(`branches.0.timings`);
                                fields.forEach((_, idx) => {
                                  if (idx !== 0) {
                                    form.setValue(`branches.${idx}.timings`, [
                                      ...sourceBranch,
                                    ]);
                                  }
                                });
                                toast.success(
                                  "Branch timings copied successfully"
                                );
                              }}
                              className="text-sm"
                            >
                              Apply to All Branches
                            </Button>
                          )}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const {
    data: restaurantData,
    isLoading,
    error,
    refetch: refetchRestaurant,
  } = useQuery({
    queryKey: ["restaurant", restaurantId],
    queryFn: async () => {
      if (!restaurantId) return null;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/onboarding/${restaurantId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        toast.error("Failed to fetch restaurant data");
      }

      return response.json();
    },
    enabled: !!restaurantId,
  });

  useEffect(() => {
    if (restaurantData) {
      const restaurantDataRes = restaurantData.data;
      if (!restaurantDataRes) return;
      setIsOnlyForListing(
        restaurantDataRes.restaurant.is_only_for_listing || false
      );
      form.reset({
        restaurant: {
          restaurant_name: restaurantDataRes.restaurant.restaurant_name,
          restaurant_id: restaurantDataRes.restaurant.restaurant_id,
          phone_number: restaurantDataRes.restaurant.phone_number,
          is_only_for_listing:
            restaurantDataRes.restaurant.is_only_for_listing || false,
          inbox_access_enabled:
            restaurantDataRes.restaurant.inbox_access_enabled || false,
          enable_support:
            restaurantDataRes.restaurant.enable_support || false,
          restaurant_auto_accept:
            restaurantDataRes.restaurant.restaurant_auto_accept || false,
          pickup_module:
            restaurantDataRes.restaurant.pickup_module || false,
        },
        branches:
          restaurantDataRes.branches.length === 0
            ? [
              {
                branch_id: "",
                branch_name: "",
                branch_address: "",
                google_maps_link: "",
                latitude: 0,
                longitude: 0,
                timings: defaultTimings,
                payment_config: defaultPaymentConfig,
              },
            ]
            : restaurantDataRes.branches.map((branch: any) => ({
              branch_id: branch.branch_id,
              branch_name: branch.branch_name,
              branch_address: branch.branch_address,
              google_maps_link: branch.google_maps_link,
              latitude: branch.latitude,
              longitude: branch.longitude,
              branch_delivery:
                branch.branch_delivery !== undefined
                  ? branch.branch_delivery
                  : true,
              branch_pickup:
                branch.branch_pickup !== undefined
                  ? branch.branch_pickup
                  : false,
              timings: branch.timings,
              no_of_orders: branch.no_of_orders || 0,
              driver_module: branch.driver_module || false,
              payment_config: branch.payment_config || defaultPaymentConfig,
              payment_methods: branch.payment_methods || defaultBranchPaymentMethods,
              branch_preparation_time: branch.branch_preparation_time || 0,
              branch_min_cart_amount: branch.branch_min_cart_amount || 0,
            })),
      });
    }
  }, [restaurantData, form]);

  if (error) {
    return (
      <main className="flex-1 flex flex-col bg-gradient-to-b  ">
        <ErrorComponent message={error.message} />
      </main>
    );
  }
  return (
    <main className="flex-1 flex flex-col bg-gradient-to-b  ">
      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-gray-500">Loading restaurant data...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Brand Section */}
          <div className="bg-white border-b">
            <div className="max-w-[1400px] mx-auto py-6 px-6">
              <div className="flex flex-col space-y-6">
                {/* Top Bar */}
                <div className="flex items-center justify-between">
                  <div
                    className="flex items-center space-x-8 cursor-pointer"
                    onClick={() => setStep(1)}
                  >
                    {" "}
                    {/* <Link href="/admin/food">
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                    </Link> */}
                    <div className="flex items-center gap-4">
                      <Link href="/admin/food">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                      </Link>
                      <div className="space-y-1">
                        <h1 className="text-2xl font-semibold text-gray-900">
                          {restaurantId ? "Edit Restaurant" : "New Restaurant"}
                        </h1>
                        <p className="text-sm text-gray-500">
                          {restaurantId
                            ? "Update your restaurant details"
                            : "Configure a new restaurant on Cravin"}
                        </p>
                      </div>
                    </div>
                    {/* <Image
                      alt="Cravin Logo"
                      src={cravinLogo}
                      priority={true}
                      width={140}
                      className="h-auto"
                    /> */}
                    <div className="hidden md:flex items-center space-x-1 bg-gray-50 px-4 py-1.5 rounded-full">
                      <span className="text-sm font-medium text-gray-600">
                        Step {step}
                      </span>
                      <span className="text-sm text-gray-400">/</span>
                      <span className="text-sm text-gray-500">
                        {totalSteps}
                      </span>
                      <Progress
                        value={(step / totalSteps) * 100}
                        className="w-24 h-1 ml-3"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col">
                    {restaurantId && (
                      <div className="flex items-center space-x-3">
                        <Link
                          href={
                            "/admin/food/onboarding/listing-details" +
                            (restaurantId ? `?id=${restaurantId}` : "")
                          }
                        >
                          <Button
                            variant="outline"
                            className="flex items-center gap-2 bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300 transition-all duration-200"
                          >
                            <Building2 className="h-4 w-4" />
                            <span className="font-medium">Branch Listings</span>
                          </Button>
                        </Link>
                        {!restaurantData?.data?.restaurant
                          ?.is_only_for_listing && (
                            <>
                              <WhatsAppSetupButton
                                businessType="food"
                                businessId={restaurantId}
                              />

                              <Button
                                variant="outline"
                                className="flex items-center gap-2 bg-white hover:bg-blue-50 text-blue-600 border-blue-200 hover:border-blue-300 transition-all duration-200"
                                onClick={() =>
                                  window.open(
                                    `https://qranalytica.com/tool/qr-code-generator?id=${restaurantId}&whatsapp_number=${restaurantData?.data?.restaurant?.phone_number}&name=${restaurantData?.data?.restaurant?.restaurant_name}&ref=Cravin`,
                                    "_blank"
                                  )
                                }
                              >
                                <QrCode className="h-4 w-4" />
                                <span className="font-medium">
                                  Generate QR Code
                                </span>
                              </Button>

                              {restaurantData?.data?.branches?.length > 0 && (
                                <Button
                                  variant="secondary"
                                  className="flex items-center gap-2"
                                  onClick={() => {
                                    setCurrentRestaurantId(restaurantId);
                                    setCurrentBranches(
                                      restaurantData?.data?.branches?.map(
                                        (branch: {
                                          branch_id?: string;
                                          branch_name?: string;
                                        }) => ({
                                          branch_id: branch.branch_id || "",
                                          branch_name: branch.branch_name || "",
                                        })
                                      )
                                    );
                                    setOnboardingComplete(true);
                                  }}
                                >
                                  <ShieldCheck className="h-4 w-4" />
                                  <span className="font-medium">
                                    Setup Logins
                                  </span>
                                </Button>
                              )}
                            </>
                          )}
                      </div>
                    )}
                    {restaurantId &&
                      !restaurantData?.data?.restaurant
                        ?.is_only_for_listing && (
                        <div className="flex items-center space-x-6 mt-4">
                          {restaurantData?.data?.restaurant?.phone_number && (
                            <a
                              href={`https://wa.me/${restaurantData?.data?.restaurant?.phone_number}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 text-green-600 hover:text-green-700"
                            >
                              <FaWhatsapp className="h-4 w-4" />
                              <span className="text-sm font-medium">
                                WhatsApp Number{" "}
                              </span>
                            </a>
                          )}
                          <a
                            href={`https://customer.justcravin.com/food/${restaurantId}/menu`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center space-x-2 text-primary hover:text-primary/80"
                          >
                            <Globe className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              View Customer Menu
                            </span>
                          </a>

                          <a
                            href={"https://food.justcravin.com/"}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center space-x-2 text-primary hover:text-primary/80"
                          >
                            <Globe className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              Merchant Dashboard
                            </span>
                          </a>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-auto">
              <div className="max-w-[1400px] mx-auto py-6 px-6">
                {onboardingComplete &&
                  currentRestaurantId &&
                  currentBranches.length > 0 ? (
                  <RestaurantLoginSetup
                    restaurantId={currentRestaurantId}
                    branches={currentBranches.filter(
                      (
                        branch
                      ): branch is { branch_id: string; branch_name: string } =>
                        typeof branch.branch_id === "string" &&
                        typeof branch.branch_name === "string"
                    )}
                    onBack={handleBackToOnboarding}
                  />
                ) : (
                  <Card className="border shadow-sm">
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="flex flex-col min-h-0"
                      >
                        {/* Form Content */}
                        <div className="p-8 space-y-6">
                          {renderStepContent()}
                        </div>

                        {/* Navigation */}
                        {!onboardingComplete && (
                          <div className="border-t p-4  mt-auto">
                            <div className="flex justify-between items-center max-w-[1400px] mx-auto">
                              <Button
                                type="button"
                                variant="outline"
                                onClick={prevStep}
                                disabled={step === 1}
                                className="w-[100px]"
                              >
                                <ChevronLeft className="h-4 w-4 mr-2" />
                                Back
                              </Button>

                              <div className="flex items-center gap-4">
                                <span className="text-sm text-gray-500">
                                  Step {step} of {totalSteps}
                                </span>
                                {step === totalSteps && (
                                  <Button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="w-[140px]"
                                  >
                                    {isSubmitting ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Submitting...
                                      </>
                                    ) : (
                                      <>
                                        Update Details
                                        <ChevronRight className="h-4 w-4 ml-2" />
                                      </>
                                    )}
                                  </Button>
                                )}
                                {step === 1 && !restaurantId && (
                                  <Button
                                    type="button"
                                    onClick={nextStep}
                                    disabled={isSubmitting}
                                    className="w-[140px]"
                                  >
                                    {isSubmitting ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Creating...
                                      </>
                                    ) : (
                                      <>
                                        Create Restaurant
                                        <ChevronRight className="h-4 w-4 ml-2" />
                                      </>
                                    )}
                                  </Button>
                                )}
                                {step < totalSteps && restaurantId && (
                                  <Button
                                    type="button"
                                    onClick={nextStep}
                                    className="w-[100px]"
                                  >
                                    Next
                                    <ChevronRight className="h-4 w-4 ml-2" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </form>
                    </Form>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </main>
  );
}

// add suspense and return fallback UI
export default function MainListingDetailsPage() {
  return (
    <Suspense
      fallback={<Loader2 className="h-8 w-8 animate-spin text-primary" />}
    >
      <OnboardingPage />
    </Suspense>
  );
}
